import React, { useState, useEffect } from "react";
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { motion } from "framer-motion";
import { useIsMobile } from "@/hooks/use-mobile";
import {
  Coffee,
  Clock,
  TrendingUp,
  TrendingDown,
  Minus,
  BarChart3,
  <PERSON><PERSON><PERSON> as PieChartIcon,
  Brain,
  AlertCircle,
  CheckCircle,
  XCircle,
  Sparkles
} from "lucide-react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  PieValueType,
  pieArcLabelClasses
} from '@mui/x-charts';
import { ThemeProvider } from '@mui/material';
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { BreakAnalytics, processBreakAnalytics } from '@/utils/breakAnalytics';
import { secondsToHours } from '@/utils/timeUtils';

interface BreakAnalysisTabProps {
  studySessions: any[];
  formatDuration: (seconds: number) => string;
  theme: 'light' | 'dark';
  muiTheme: any;
}

const BreakAnalysisTab: React.FC<BreakAnalysisTabProps> = ({
  studySessions,
  formatDuration,
  theme,
  muiTheme
}) => {
  const [breakAnalytics, setBreakAnalytics] = useState<BreakAnalytics | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [showInsights, setShowInsights] = useState(false);
  const [isLoadingInsights, setIsLoadingInsights] = useState(false);
  const isDarkMode = theme === "dark";
  const isMobile = useIsMobile();

  const handleRequestInsights = async () => {
    if (!breakAnalytics || isLoadingInsights) return;

    setIsLoadingInsights(true);
    try {
      // Re-process analytics with AI insights
      const analyticsWithInsights = await processBreakAnalytics(studySessions, true);
      setBreakAnalytics(analyticsWithInsights);
      setShowInsights(true);
    } catch (error) {
      console.error('Error generating insights:', error);
    } finally {
      setIsLoadingInsights(false);
    }
  };

  useEffect(() => {
    const loadBreakAnalytics = async () => {
      setIsLoading(true);
      try {
        const analytics = await processBreakAnalytics(studySessions, false);
        setBreakAnalytics(analytics);
      } catch (error) {
        console.error('Error processing break analytics:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadBreakAnalytics();
  }, [studySessions]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-48 sm:h-64">
        <div className="flex flex-col items-center gap-3">
          <div className="animate-spin rounded-full h-6 w-6 sm:h-8 sm:w-8 border-b-2 border-violet-600"></div>
          <p className="text-sm text-muted-foreground">Analyzing break patterns...</p>
        </div>
      </div>
    );
  }

  if (!breakAnalytics || breakAnalytics.durationStats.breakCount === 0) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="text-center py-12"
      >
        <Coffee className="h-16 w-16 text-muted-foreground mx-auto mb-4 opacity-50" />
        <h3 className="text-xl font-semibold text-foreground mb-2">No Break Data Available</h3>
        <p className="text-muted-foreground">
          Start using the study timer and take breaks to see your break patterns here.
        </p>
      </motion.div>
    );
  }

  const { durationStats, reasonAnalysis, frequencyData, insights } = breakAnalytics;

  // Prepare data for charts
  const reasonChartData = reasonAnalysis.slice(0, 6).map((reason, index) => ({
    id: index,
    value: reason.count,
    label: reason.reason,
    color: getReasonColor(reason.reason, isDarkMode)
  }));

  const frequencyChartData = frequencyData.slice(-14).map(day => ({
    date: new Date(day.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
    breaks: day.breakCount,
    rate: day.breakFrequencyRate
  }));

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <h2 className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-violet-600 via-purple-600 to-rose-600 mb-2">
          Break Analysis
        </h2>
        <p className="text-muted-foreground">
          Understand your break patterns and improve your focus
        </p>
      </motion.div>

      {/* Overview Stats */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.1 }}
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"
      >
        <Card className="bg-card/80 backdrop-blur-sm border shadow-lg">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Breaks</p>
                <p className="text-2xl font-bold text-violet-600">{durationStats.breakCount}</p>
              </div>
              <Coffee className="h-8 w-8 text-violet-600/60" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-card/80 backdrop-blur-sm border shadow-lg">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Avg Duration</p>
                <p className="text-2xl font-bold text-emerald-600">
                  {formatDuration(durationStats.averageDuration)}
                </p>
              </div>
              <Clock className="h-8 w-8 text-emerald-600/60" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-card/80 backdrop-blur-sm border shadow-lg">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Longest Break</p>
                <p className="text-2xl font-bold text-rose-600">
                  {formatDuration(durationStats.longestBreak)}
                </p>
              </div>
              <TrendingUp className="h-8 w-8 text-rose-600/60" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-card/80 backdrop-blur-sm border shadow-lg">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Break Time</p>
                <p className="text-2xl font-bold text-purple-600">
                  {formatDuration(durationStats.totalBreakTime)}
                </p>
              </div>
              <BarChart3 className="h-8 w-8 text-purple-600/60" />
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Charts Row */}
      <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
        {/* Break Reasons Pie Chart */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <Card className="bg-card/80 backdrop-blur-sm border shadow-lg h-full">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-medium flex items-center gap-2">
                <PieChartIcon className="h-5 w-5 text-violet-600" />
                <span>Break Reasons</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="h-[400px]">
              <ThemeProvider theme={muiTheme}>
                <PieChart
                  series={[
                    {
                      data: reasonChartData,
                      highlightScope: { faded: 'global', highlighted: 'item' },
                      faded: { innerRadius: 30, additionalRadius: -30, color: 'gray' },
                      valueFormatter: (item: PieValueType) => `${item.value} times`,
                    },
                  ]}
                  slotProps={{
                    legend: {
                      direction: 'column',
                      position: { vertical: 'middle', horizontal: 'right' },
                      padding: 0,
                      itemMarkWidth: 8,
                      itemMarkHeight: 8,
                      markGap: 4,
                      itemGap: 6,
                    },
                  }}
                  margin={{
                    right: isMobile ? 100 : 200,
                    left: 20,
                    top: 20,
                    bottom: 20
                  }}
                />
              </ThemeProvider>
            </CardContent>
          </Card>
        </motion.div>

        {/* Break Frequency Trend */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
        >
          <Card className="bg-card/80 backdrop-blur-sm border shadow-lg h-full">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-medium flex items-center gap-2">
                <TrendingUp className="h-5 w-5 text-emerald-600" />
                <span>Break Frequency Trend</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="h-[400px]">
              <ThemeProvider theme={muiTheme}>
                <LineChart
                  xAxis={[
                    {
                      scaleType: 'point',
                      data: frequencyChartData.map(d => d.date),
                      tickLabelStyle: {
                        fontSize: isMobile ? 10 : 12,
                        angle: isMobile ? -45 : 0,
                      },
                    },
                  ]}
                  series={[
                    {
                      data: frequencyChartData.map(d => d.breaks),
                      label: 'Daily Breaks',
                      color: '#8b5cf6',
                      curve: 'linear',
                    },
                  ]}
                  height={350}
                  margin={{
                    top: 20,
                    right: isMobile ? 20 : 30,
                    bottom: isMobile ? 80 : 60,
                    left: isMobile ? 40 : 60
                  }}
                  slotProps={{
                    legend: {
                      direction: 'row',
                      position: { vertical: 'bottom', horizontal: 'middle' },
                      padding: 10,
                    },
                  }}
                />
              </ThemeProvider>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* AI Insights Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.4 }}
      >
        <Card className="bg-card/80 backdrop-blur-sm border shadow-lg">
          <CardHeader className="pb-4">
            <CardTitle className="text-lg font-medium flex items-center gap-2">
              <Brain className="h-5 w-5 text-violet-600" />
              <span>AI-Powered Insights</span>
              <Badge variant="secondary" className="ml-2 bg-violet-100 text-violet-700 dark:bg-violet-900/30 dark:text-violet-300">
                Powered by Gemini
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {!showInsights ? (
              <div className="text-center py-8">
                <p className="text-muted-foreground mb-4">
                  Get personalized insights about your break patterns and productivity recommendations.
                </p>
                <Button
                  onClick={handleRequestInsights}
                  disabled={isLoadingInsights || !breakAnalytics || breakAnalytics.durationStats.breakCount === 0}
                  className="bg-gradient-to-r from-violet-600 to-purple-600 hover:from-violet-700 hover:to-purple-700"
                >
                  {isLoadingInsights ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b border-white mr-2"></div>
                      Analyzing Patterns...
                    </>
                  ) : (
                    <>
                      <Sparkles className="h-4 w-4 mr-2" />
                      Generate AI Insights
                    </>
                  )}
                </Button>
              </div>
            ) : (
              insights && insights.length > 0 && (
                <div className="space-y-3">
                  {insights.map((insight, index) => (
                    <Alert key={index} className="border-l-4 border-l-violet-500 bg-violet-50/50 dark:bg-violet-900/10">
                      <AlertCircle className="h-4 w-4 text-violet-600" />
                      <AlertDescription className="text-sm text-foreground">
                        {insight}
                      </AlertDescription>
                    </Alert>
                  ))}
                </div>
              )
            )}
          </CardContent>
        </Card>
      </motion.div>

      {/* Break Reason Details */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.5 }}
      >
        <Card className="bg-card/80 backdrop-blur-sm border shadow-lg">
          <CardHeader className="pb-4">
            <CardTitle className="text-lg font-medium flex items-center gap-2">
              <BarChart3 className="h-5 w-5 text-emerald-600" />
              <span>Detailed Break Analysis</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {reasonAnalysis.slice(0, 8).map((reason, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-muted/40 rounded-lg border">
                  <div className="flex items-center gap-3">
                    <div
                      className="w-4 h-4 rounded-full"
                      style={{ backgroundColor: getReasonColor(reason.reason, isDarkMode) }}
                    />
                    <div>
                      <p className="font-medium text-foreground">{reason.reason}</p>
                      <p className="text-sm text-muted-foreground">
                        {reason.count} times • Avg: {formatDuration(reason.averageDuration)}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="text-xs">
                      {reason.percentage.toFixed(1)}%
                    </Badge>
                    {reason.trend === 'increasing' && (
                      <TrendingUp className="h-4 w-4 text-red-500" />
                    )}
                    {reason.trend === 'decreasing' && (
                      <TrendingDown className="h-4 w-4 text-green-500" />
                    )}
                    {reason.trend === 'stable' && (
                      <Minus className="h-4 w-4 text-gray-500" />
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
};

// Helper function to get colors for break reasons
const getReasonColor = (reason: string, isDarkMode: boolean) => {
  const colorMap: { [key: string]: string } = {
    'Fatigue/Tiredness': isDarkMode ? '#ef4444' : '#dc2626',
    'Distraction/Focus Issues': isDarkMode ? '#f97316' : '#ea580c',
    'Bathroom Break': isDarkMode ? '#06b6d4' : '#0891b2',
    'Hydration Break': isDarkMode ? '#3b82f6' : '#2563eb',
    'Food/Snack Break': isDarkMode ? '#10b981' : '#059669',
    'Phone/Social Media': isDarkMode ? '#8b5cf6' : '#7c3aed',
    'Physical Activity': isDarkMode ? '#84cc16' : '#65a30d',
    'Social Interaction': isDarkMode ? '#f59e0b' : '#d97706',
    'Difficulty with Material': isDarkMode ? '#ec4899' : '#db2777',
    'Boredom': isDarkMode ? '#6b7280' : '#4b5563',
    'No Reason Provided': isDarkMode ? '#64748b' : '#475569',
    'Other': isDarkMode ? '#a855f7' : '#9333ea'
  };
  
  return colorMap[reason] || (isDarkMode ? '#6366f1' : '#4f46e5');
};

export default BreakAnalysisTab;
